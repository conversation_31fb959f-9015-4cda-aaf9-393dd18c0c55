# HT LangChain Multi-Agent System MVP v2

基于 LangChain.js 和 LangGraph 的多智能体协作系统，实现复杂任务的智能化处理和协作执行。

## 🌟 特性

- **多智能体协作**: 实现 Agent A、B、C、D 的智能协作
- **并行执行**: Agent A 和 Agent B 并行处理，提高效率
- **迭代优化**: Agent B 支持多轮迭代，深化分析质量
- **函数处理**: Agent C 提供代码函数执行能力
- **结果整合**: Agent D 整合所有智能体结果，输出最终答案
- **状态持久化**: 基于 MongoDB 的会话和状态管理
- **性能监控**: 完整的执行指标和性能统计
- **错误恢复**: 多层错误处理和恢复机制

## 🏗️ 架构设计

### 工作流程图

```mermaid
graph TD
    START([__start__]) --> NA[agent_a]
    START --> NB[agent_b]
    NA --> NC[agent_c]
    NC --> ND[agent_d]
    NB --> ND[agent_d]
    ND --> END([__end__])
```

### 智能体说明

- **Agent A**: 深度分析智能体，处理初始输入并提供专业分析
- **Agent B**: 迭代优化智能体，支持多轮思考，持续改进分析质量
- **Agent C**: 函数执行智能体，执行 `codeA + "xxxxdx"` 函数逻辑
- **Agent D**: 结果整合智能体，汇聚所有智能体输出，生成最终回答

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- MongoDB >= 5.0
- OpenAI API Key

### 安装和配置

1. **克隆项目**
```bash
git clone <repository-url>
cd ht-langchain-mvp-v2
```

2. **安装依赖**
```bash
npm install --legacy-peer-deps
```

3. **配置环境变量**
```bash
cp env.template .env
# 编辑 .env 文件，填写必要的配置
```

环境变量说明：
```bash
# API配置
OPENAI_API_KEY=sk-cITab2RRLmsfbUH-pNJg
OPENAI_API_BASE=http://*************/web/unauth/LLM_api_proxy/v1
OPENAI_MODEL_NAME=ht::saas-deepseek-v3

# MongoDB配置
MONGODB_URI=mongodb://localhost:27017/langgraph
MONGODB_DB_NAME=langgraph

# 服务器配置
PORT=3000
NODE_ENV=development
```

4. **构建项目**
```bash
npm run build
```

5. **启动服务**
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 验证安装

运行测试脚本验证系统功能：
```bash
npx ts-node scripts/test-workflow.ts
```

## 📖 API 文档

### 执行工作流

**POST** `/api/workflow/execute`

请求体：
```json
{
  "input": "请分析人工智能的发展趋势",
  "user_id": "user123" // 可选
}
```

响应：
```json
{
  "success": true,
  "data": {
    "session_id": "uuid",
    "output": "多智能体协作分析结果...",
    "execution_metadata": {
      "total_execution_time": 5234,
      "agent_execution_times": {
        "agent_a": 1200,
        "agent_b": 1800,
        "agent_c": 34,
        "agent_d": 1500
      },
      "tokens_used": {
        "agent_a": 150,
        "agent_b": 200,
        "agent_d": 180,
        "total": 530
      }
    }
  },
  "timestamp": "2024-12-18T10:30:00.000Z"
}
```

### 获取会话历史

**GET** `/api/workflow/session/{session_id}`

### 获取用户会话列表

**GET** `/api/workflow/sessions/user/{user_id}`

### 搜索会话

**GET** `/api/workflow/sessions/search?q=搜索关键词`

### 系统统计

**GET** `/api/workflow/stats`

### 健康检查

**GET** `/api/workflow/health`

## 🧪 测试

### 运行单元测试
```bash
npm test
```

### 运行集成测试
```bash
npm run test:integration
```

### 手动测试
```bash
npx ts-node scripts/test-workflow.ts
```

## 📊 监控和日志

### 日志文件
- `logs/app.log` - 应用日志
- `logs/error.log` - 错误日志
- `logs/exceptions.log` - 异常日志

### 性能指标
系统提供以下性能指标：
- 智能体执行时间
- Token 使用量
- 错误率和成功率
- 系统吞吐量

## 🔧 开发指南

### 项目结构
```
src/
├── agents/           # 智能体实现
│   ├── agent-a.ts   # Agent A 深度分析
│   ├── agent-b.ts   # Agent B 迭代优化
│   ├── agent-c.ts   # Agent C 函数执行
│   ├── agent-d.ts   # Agent D 结果整合
│   └── workflow.ts  # 工作流管理
├── config/          # 配置管理
├── database/        # 数据库连接和服务
├── routes/          # API 路由
├── types/           # 类型定义
└── utils/           # 工具函数
```

### 添加新智能体

1. 在 `src/agents/` 目录创建新智能体文件
2. 实现标准接口：
   ```typescript
   interface Agent {
     execute(input: any, sessionId: string): Promise<NodeExecutionResult>;
     healthCheck(): Promise<boolean>;
     getConfig(): any;
   }
   ```
3. 在工作流中注册新智能体
4. 更新类型定义和测试用例

### 自定义配置

修改 `src/config/index.ts` 中的 `defaultAgentConfigs` 来自定义智能体配置：

```typescript
export const defaultAgentConfigs = {
  agent_a: {
    name: 'Custom Agent A',
    model: 'gpt-4o-mini',
    temperature: 0.7,
    systemPrompt: '你的自定义提示词...'
  }
  // ... 其他配置
};
```

## 🐛 故障排除

### 常见问题

1. **MongoDB 连接失败**
   - 检查 MongoDB 服务是否运行
   - 验证 `MONGODB_URI` 配置
   - 确认网络连接

2. **OpenAI API 调用失败**
   - 验证 `OPENAI_API_KEY` 是否正确
   - 检查 API 配额和限制
   - 确认网络连接

3. **内存不足**
   - 增加 Node.js 内存限制：`node --max-old-space-size=4096`
   - 优化模型参数和 `maxTokens` 设置

4. **性能问题**
   - 检查数据库索引
   - 监控智能体执行时间
   - 调整并发设置

### 调试模式

启用详细日志：
```bash
LOG_LEVEL=debug npm run dev
```

## 📈 性能优化

### 建议配置

1. **生产环境优化**
   ```bash
   NODE_ENV=production
   LOG_LEVEL=warn
   ```

2. **数据库优化**
   - 使用 MongoDB 副本集
   - 配置适当的连接池大小
   - 定期清理旧数据

3. **API 限制**
   - 实现请求频率限制
   - 配置超时设置
   - 使用缓存减少重复调用

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🆘 支持

如有问题或建议，请：
1. 查看文档和 FAQ
2. 搜索已有 Issues
3. 创建新的 Issue
4. 联系维护团队

---

**版本**: 1.0.0  
**最后更新**: 2024年12月  
**维护团队**: HT Team
