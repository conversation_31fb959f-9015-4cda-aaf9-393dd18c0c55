# 设计文档

## 概述

本设计文档描述了基于 LangChain.js 和 LangGraph 的多智能体系统 MVP 的技术架构。系统采用工作流编排模式，通过 LangGraph 管理多个智能体节点的协作执行，支持条件分支、数据持久化和实时监控。

## 架构

### 整体架构

```mermaid
graph TB
    API[REST API 层] --> WF[工作流管理器]
    WF --> LG[LangGraph 引擎]
    
    LG --> NA[Agent A - LLM Agent]
    LG --> NB[Agent B - LLM Agent]
    NA --> NC[Agent C - Function Agent]
    NC --> ND[Agent D - LLM Agent]
    NB --> ND
    
    WF --> MON[监控服务]
    WF --> STORE[存储服务]
    STORE --> MONGO[(MongoDB)]
    STORE --> CACHE[本地缓存]
    
    NA --> SANDBOX1[沙箱环境A]
    NB --> SANDBOX2[沙箱环境B]
    NC --> SANDBOX3[沙箱环境C]
    ND --> SANDBOX4[沙箱环境D]
    
    subgraph "并行执行"
        NA
        NB
    end
    
    subgraph "汇聚执行"
        ND
    end
```

### 工作流执行流程

基于提供的 LangGraph 设计图，工作流执行流程如下：

```mermaid
graph TD
    START([__start__]) --> NA[agent_a]
    START --> NB[agent_b]
    NA --> NC[agent_c]
    NC --> ND[agent_d]
    NB --> ND[agent_d]
    ND --> END([__end__])
```

**流程说明：**
1. **__start__**: 工作流入口点，初始化执行上下文
2. **并行执行阶段**:
   - **agent_a**: 第一个 LLM 智能体，与 agent_b 同时开始执行
   - **agent_b**: 第二个 LLM 智能体，与 agent_a 并行执行，支持多次 LLM 调用
3. **顺序执行阶段**:
   - **agent_c**: 函数智能体，等待 agent_a 完成后执行，可以获取 agent_a 的执行结果，执行 `codeA + "xxxxdx"` 函数调用
4. **汇聚阶段**:
   - **agent_d**: 最终的 LLM 智能体，等待 agent_b 和 agent_c 都完成后执行，整合所有前面节点的结果
5. **__end__**: 工作流结束点，输出最终结果

**执行时序：**
- t1: agent_a 和 agent_b 同时开始执行
- t2: agent_a 完成后，agent_c 开始执行（可访问 agent_a 的结果）
- t3: 当 agent_b 和 agent_c 都完成后，agent_d 开始执行
- t4: agent_d 完成，工作流结束
