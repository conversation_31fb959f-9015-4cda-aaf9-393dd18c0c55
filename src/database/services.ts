import { DatabaseConnection } from './connection';
import { ConversationHistory, AgentExecutionRecord, Message } from '../types';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

export class ConversationService {
  private db: DatabaseConnection;

  constructor() {
    this.db = DatabaseConnection.getInstance();
  }

  // 创建新会话
  async createSession(userId?: string): Promise<string> {
    try {
      const sessionId = uuidv4();
      const conversationsCollection = this.db.getConversationsCollection();

      const conversation: ConversationHistory = {
        session_id: sessionId,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date(),
        messages: [],
        state_snapshots: []
      };

      await conversationsCollection.insertOne(conversation);
      
      logger.info('New session created', { session_id: sessionId, user_id: userId });
      return sessionId;
    } catch (error: any) {
      logger.error('Failed to create session', { error, user_id: userId });
      throw new Error(`Failed to create session: ${error.message}`);
    }
  }

  // 获取会话历史
  async getSessionHistory(sessionId: string): Promise<ConversationHistory | null> {
    try {
      const conversationsCollection = this.db.getConversationsCollection();
      const conversation = await conversationsCollection.findOne({ session_id: sessionId });
      
      if (!conversation) {
        logger.warn('Session not found', { session_id: sessionId });
        return null;
      }

      return conversation;
    } catch (error: any) {
      logger.error('Failed to get session history', { error, session_id: sessionId });
      throw new Error(`Failed to get session history: ${error.message}`);
    }
  }

  // 添加消息到会话
  async addMessage(sessionId: string, message: Message): Promise<void> {
    try {
      const conversationsCollection = this.db.getConversationsCollection();
      
      await conversationsCollection.updateOne(
        { session_id: sessionId },
        {
          $push: { messages: message },
          $set: { updated_at: new Date() }
        }
      );

      logger.debug('Message added to session', { 
        session_id: sessionId, 
        role: message.role,
        content_length: message.content.length 
      });
    } catch (error: any) {
      logger.error('Failed to add message', { error, session_id: sessionId });
      throw new Error(`Failed to add message: ${error.message}`);
    }
  }

  // 获取用户的所有会话
  async getUserSessions(userId: string, limit: number = 20, offset: number = 0): Promise<ConversationHistory[]> {
    try {
      const conversationsCollection = this.db.getConversationsCollection();
      
      const sessions = await conversationsCollection
        .find({ user_id: userId })
        .sort({ created_at: -1 })
        .skip(offset)
        .limit(limit)
        .toArray();

      logger.debug('Retrieved user sessions', { 
        user_id: userId, 
        count: sessions.length,
        limit,
        offset 
      });

      return sessions;
    } catch (error: any) {
      logger.error('Failed to get user sessions', { error, user_id: userId });
      throw new Error(`Failed to get user sessions: ${error.message}`);
    }
  }

  // 删除会话
  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      const conversationsCollection = this.db.getConversationsCollection();
      const executionsCollection = this.db.getAgentExecutionsCollection();

      // 删除会话记录
      const conversationResult = await conversationsCollection.deleteOne({ session_id: sessionId });
      
      // 删除相关的执行记录
      await executionsCollection.deleteMany({ session_id: sessionId });

      const deleted = conversationResult.deletedCount > 0;
      
      if (deleted) {
        logger.info('Session deleted', { session_id: sessionId });
      } else {
        logger.warn('Session not found for deletion', { session_id: sessionId });
      }

      return deleted;
    } catch (error: any) {
      logger.error('Failed to delete session', { error, session_id: sessionId });
      throw new Error(`Failed to delete session: ${error.message}`);
    }
  }

  // 搜索会话
  async searchSessions(
    query: string, 
    userId?: string, 
    limit: number = 10
  ): Promise<ConversationHistory[]> {
    try {
      const conversationsCollection = this.db.getConversationsCollection();
      
      const searchFilter: any = {
        $or: [
          { 'messages.content': { $regex: query, $options: 'i' } },
          { 'final_result.output': { $regex: query, $options: 'i' } }
        ]
      };

      if (userId) {
        searchFilter.user_id = userId;
      }

      const sessions = await conversationsCollection
        .find(searchFilter)
        .sort({ updated_at: -1 })
        .limit(limit)
        .toArray();

      logger.debug('Sessions searched', { 
        query, 
        user_id: userId, 
        results_count: sessions.length 
      });

      return sessions;
    } catch (error: any) {
      logger.error('Failed to search sessions', { error, query, user_id: userId });
      throw new Error(`Failed to search sessions: ${error.message}`);
    }
  }
}

export class AgentExecutionService {
  private db: DatabaseConnection;

  constructor() {
    this.db = DatabaseConnection.getInstance();
  }

  // 获取会话的执行记录
  async getSessionExecutions(sessionId: string): Promise<AgentExecutionRecord[]> {
    try {
      const executionsCollection = this.db.getAgentExecutionsCollection();
      
      const executions = await executionsCollection
        .find({ session_id: sessionId })
        .sort({ execution_start: 1 })
        .toArray();

      logger.debug('Retrieved session executions', { 
        session_id: sessionId, 
        count: executions.length 
      });

      return executions;
    } catch (error: any) {
      logger.error('Failed to get session executions', { error, session_id: sessionId });
      throw new Error(`Failed to get session executions: ${error.message}`);
    }
  }

  // 获取节点的性能统计
  async getNodePerformanceStats(nodeId: string, hours: number = 24): Promise<any> {
    try {
      const executionsCollection = this.db.getAgentExecutionsCollection();
      const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);

      const stats = await executionsCollection.aggregate([
        {
          $match: {
            node_id: nodeId,
            execution_start: { $gte: startTime },
            'performance_metrics.latency_ms': { $exists: true }
          }
        },
        {
          $group: {
            _id: '$node_id',
            total_executions: { $sum: 1 },
            avg_latency: { $avg: '$performance_metrics.latency_ms' },
            min_latency: { $min: '$performance_metrics.latency_ms' },
            max_latency: { $max: '$performance_metrics.latency_ms' },
            total_tokens: { $sum: '$performance_metrics.tokens_used' },
            avg_tokens: { $avg: '$performance_metrics.tokens_used' },
            error_count: {
              $sum: {
                $cond: [{ $exists: ['$error_info'] }, 1, 0]
              }
            }
          }
        }
      ]).toArray();

      const result = stats[0] || {
        _id: nodeId,
        total_executions: 0,
        avg_latency: 0,
        min_latency: 0,
        max_latency: 0,
        total_tokens: 0,
        avg_tokens: 0,
        error_count: 0
      };

      logger.debug('Retrieved node performance stats', { 
        node_id: nodeId, 
        hours,
        stats: result 
      });

      return result;
    } catch (error: any) {
      logger.error('Failed to get node performance stats', { error, node_id: nodeId });
      throw new Error(`Failed to get node performance stats: ${error.message}`);
    }
  }

  // 获取系统整体统计
  async getSystemStats(hours: number = 24): Promise<any> {
    try {
      const executionsCollection = this.db.getAgentExecutionsCollection();
      const conversationsCollection = this.db.getConversationsCollection();
      const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);

      // 执行统计
      const executionStats = await executionsCollection.aggregate([
        {
          $match: {
            execution_start: { $gte: startTime }
          }
        },
        {
          $group: {
            _id: null,
            total_executions: { $sum: 1 },
            avg_latency: { $avg: '$performance_metrics.latency_ms' },
            total_tokens: { $sum: '$performance_metrics.tokens_used' },
            error_count: {
              $sum: {
                $cond: [{ $exists: ['$error_info'] }, 1, 0]
              }
            }
          }
        }
      ]).toArray();

      // 会话统计
      const sessionStats = await conversationsCollection.aggregate([
        {
          $match: {
            created_at: { $gte: startTime }
          }
        },
        {
          $group: {
            _id: null,
            total_sessions: { $sum: 1 },
            successful_sessions: {
              $sum: {
                $cond: [{ $eq: ['$final_result.success', true] }, 1, 0]
              }
            }
          }
        }
      ]).toArray();

      const result = {
        period_hours: hours,
        executions: executionStats[0] || { total_executions: 0, avg_latency: 0, total_tokens: 0, error_count: 0 },
        sessions: sessionStats[0] || { total_sessions: 0, successful_sessions: 0 },
        success_rate: sessionStats[0] ? 
          (sessionStats[0].successful_sessions / sessionStats[0].total_sessions * 100) : 0,
        error_rate: executionStats[0] ? 
          (executionStats[0].error_count / executionStats[0].total_executions * 100) : 0
      };

      logger.debug('Retrieved system stats', { hours, stats: result });

      return result;
    } catch (error: any) {
      logger.error('Failed to get system stats', { error, hours });
      throw new Error(`Failed to get system stats: ${error.message}`);
    }
  }
}

// 数据库健康检查服务
export class DatabaseHealthService {
  private db: DatabaseConnection;

  constructor() {
    this.db = DatabaseConnection.getInstance();
  }

  async checkHealth(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: any;
  }> {
    try {
      const isConnected = await this.db.healthCheck();
      
      if (!isConnected) {
        return {
          status: 'unhealthy',
          details: { error: 'Database connection failed' }
        };
      }

      // 检查集合状态
      const collections = await this.db.getDb().listCollections().toArray();
      const requiredCollections = ['conversations', 'agent_executions'];
      const missingCollections = requiredCollections.filter(
        name => !collections.some(col => col.name === name)
      );

      return {
        status: missingCollections.length === 0 ? 'healthy' : 'unhealthy',
        details: {
          connected: true,
          collections: collections.map(col => col.name),
          missing_collections: missingCollections,
          total_collections: collections.length
        }
      };
    } catch (error: any) {
      logger.error('Database health check failed', { error });
      return {
        status: 'unhealthy',
        details: { error: error.message }
      };
    }
  }
}
