import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config, validateConfig } from './config';
import { DatabaseConnection } from './database/connection';
import { workflowRouter, initializeWorkflow } from './routes/workflow';
import { logger } from './utils/logger';

const app = express();

// 中间件配置
app.use(helmet()); // 安全头
app.use(cors()); // CORS支持
app.use(express.json({ limit: '10mb' })); // JSON解析
app.use(express.urlencoded({ extended: true })); // URL编码解析

// 请求日志中间件
app.use((req, res, next) => {
  logger.info('HTTP Request', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    contentLength: req.get('Content-Length')
  });
  next();
});

// API路由
app.use('/api/workflow', workflowRouter);

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'HT LangChain Multi-Agent System MVP v2',
    version: '1.0.0',
    timestamp: new Date(),
    endpoints: {
      workflow_execute: 'POST /api/workflow/execute',
      session_history: 'GET /api/workflow/session/:session_id',
      user_sessions: 'GET /api/workflow/sessions/user/:user_id',
      search_sessions: 'GET /api/workflow/sessions/search?q=query',
      system_stats: 'GET /api/workflow/stats',
      agent_stats: 'GET /api/workflow/stats/agent/:node_id',
      health_check: 'GET /api/workflow/health'
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not Found',
    message: `Route ${req.method} ${req.originalUrl} not found`,
    timestamp: new Date()
  });
});

// 全局错误处理中间件
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip
  });

  res.status(500).json({
    success: false,
    error: 'Internal Server Error',
    message: config.server.nodeEnv === 'development' ? error.message : 'An unexpected error occurred',
    timestamp: new Date()
  });
});

// 应用程序初始化
async function initializeApp(): Promise<void> {
  try {
    logger.info('Starting application initialization...');

    // 1. 验证配置
    validateConfig();
    logger.info('Configuration validated');

    // 2. 连接数据库
    const db = DatabaseConnection.getInstance();
    await db.connect();
    logger.info('Database connected');

    // 3. 初始化工作流
    await initializeWorkflow();
    logger.info('Workflow initialized');

    logger.info('Application initialization completed successfully');
  } catch (error: any) {
    logger.error('Application initialization failed', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

// 优雅关闭处理
async function gracefulShutdown(signal: string): Promise<void> {
  logger.info(`Received ${signal}, starting graceful shutdown...`);

  try {
    // 关闭数据库连接
    const db = DatabaseConnection.getInstance();
    await db.disconnect();
    logger.info('Database disconnected');

    // 其他清理工作...
    
    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error: any) {
    logger.error('Error during graceful shutdown', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  }
}

// 注册信号处理器
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', {
    error: error.message,
    stack: error.stack
  });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', {
    reason,
    promise
  });
  process.exit(1);
});

// 启动服务器
async function startServer(): Promise<void> {
  try {
    await initializeApp();

    const server = app.listen(config.server.port, () => {
      logger.info('Server started successfully', {
        port: config.server.port,
        env: config.server.nodeEnv,
        pid: process.pid
      });

      console.log(`
╔════════════════════════════════════════════════════════════════╗
║                 HT LangChain Multi-Agent System                ║
║                          MVP v2                                ║
╠════════════════════════════════════════════════════════════════╣
║ Status: ✅ Running                                             ║
║ Port: ${config.server.port.toString().padEnd(43)}              ║
║ Environment: ${config.server.nodeEnv.padEnd(35)}               ║
║ Database: MongoDB Connected                                    ║
║ Agents: Agent A, B, C, D Initialized                           ║
╠════════════════════════════════════════════════════════════════╣
║ API Endpoints:                                                 ║
║ • POST /api/workflow/execute - Execute workflow                ║
║ • GET  /api/workflow/health  - Health check                    ║
║ • GET  /api/workflow/stats   - System statistics               ║
╚════════════════════════════════════════════════════════════════╝
      `);
    });

    // 服务器错误处理
    server.on('error', (error: any) => {
      if (error.code === 'EADDRINUSE') {
        logger.error(`Port ${config.server.port} is already in use`);
      } else {
        logger.error('Server error', { error: error.message });
      }
      process.exit(1);
    });

  } catch (error: any) {
    logger.error('Failed to start server', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  }
}

// 只有在直接运行此文件时才启动服务器
if (require.main === module) {
  startServer();
}

// 导出应用实例（用于测试）
export default app;
