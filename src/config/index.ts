import dotenv from 'dotenv';
import { SystemConfig } from '../types';

// 加载环境变量
dotenv.config();

export const config: SystemConfig = {
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
    apiBase: process.env.OPENAI_API_BASE || 'https://api.openai.com/v1',
    modelName: process.env.OPENAI_MODEL_NAME || 'ht::saas-deepseek-v3'
  },
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/langgraph',
    dbName: process.env.MONGODB_DB_NAME || 'langgraph'
  },
  server: {
    port: parseInt(process.env.PORT || '3000', 10),
    nodeEnv: process.env.NODE_ENV || 'development'
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs'
  }
};

// 配置验证
export function validateConfig(): void {
  const requiredFields = [
    'openai.apiKey',
    'mongodb.uri'
  ];

  for (const field of requiredFields) {
    const value = field.split('.').reduce((obj, key) => obj?.[key], config as any);
    if (!value) {
      throw new Error(`Missing required configuration: ${field}`);
    }
  }
}

// 日志配置
export const loggerConfig = {
  level: config.logging.level,
  format: 'json',
  transports: {
    console: true,
    file: {
      enabled: true,
      filename: `${config.logging.filePath}/app.log`,
      errorFilename: `${config.logging.filePath}/error.log`
    }
  }
};

// 智能体默认配置
export const defaultAgentConfigs = {
  agent_a: {
    name: 'Agent A',
    description: '第一个LLM智能体，处理初始任务',
    model: config.openai.modelName,
    temperature: 0.7,
    maxTokens: 1000,
    systemPrompt: `你是Agent A，一个专业的智能助手。你的任务是处理用户输入并提供有价值的分析。
    请根据输入内容进行深入分析，并提供清晰、有条理的回答。`
  },
  agent_b: {
    name: 'Agent B',
    description: '第二个LLM智能体，支持多次调用',
    model: config.openai.modelName,
    temperature: 0.5,
    maxTokens: 1000,
    systemPrompt: `你是Agent B，一个具备迭代思考能力的智能助手。你可以进行多轮思考来优化答案。
    请仔细分析问题，如果需要可以进行多次迭代来完善你的回答。`
  },
  agent_c: {
    name: 'Agent C',
    description: '函数智能体，执行代码逻辑',
    model: 'function',
    temperature: 0,
    systemPrompt: `你是Agent C，一个函数执行智能体。你的主要任务是执行 codeA + "xxxxdx" 的函数调用。`
  },
  agent_d: {
    name: 'Agent D',
    description: '最终智能体，整合所有结果',
    model: config.openai.modelName,
    temperature: 0.5,
    maxTokens: 3000,
    systemPrompt: `你是Agent D，负责整合前面所有智能体的结果。
    请综合Agent A、Agent B和Agent C的输出，提供一个完整、准确的最终答案。`
  }
};
