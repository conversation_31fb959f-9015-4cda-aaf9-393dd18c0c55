import { Router, Request, Response } from 'express';
import { SimpleMultiAgentWorkflow } from '../agents/simple-workflow';
import { ConversationService, AgentExecutionService, DatabaseHealthService } from '../database/services';
import { logger, PerformanceLogger } from '../utils/logger';
import { ApiResponse, Message } from '../types';
import Joi from 'joi';

export const workflowRouter = Router();

// 初始化服务
const workflow = new SimpleMultiAgentWorkflow();
const conversationService = new ConversationService();
const executionService = new AgentExecutionService();
const healthService = new DatabaseHealthService();

// 输入验证schemas
const executeWorkflowSchema = Joi.object({
  input: Joi.string().required().min(1).max(10000),
  user_id: Joi.string().optional().max(100)
});

const getSessionSchema = Joi.object({
  session_id: Joi.string().required().uuid()
});

// 初始化工作流
async function initializeWorkflow() {
  try {
    await workflow.compile();
    logger.info('Workflow routes initialized successfully');
  } catch (error: any) {
    logger.error('Failed to initialize workflow', { error });
    throw error;
  }
}

// 执行多智能体工作流
workflowRouter.post('/execute', async (req: Request, res: Response) => {
  const perfLogger = new PerformanceLogger('workflow_api_execute');
  
  try {
    // 验证输入
    const { error, value } = executeWorkflowSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Invalid input',
        message: error.details[0].message,
        timestamp: new Date()
      } as ApiResponse);
    }

    const { input, user_id } = value;
    
    logger.info('Workflow execution requested', { 
      input_length: input.length,
      user_id,
      ip: req.ip 
    });

    perfLogger.checkpoint('input_validated');

    // 执行工作流
    const result = await workflow.execute(input, user_id);
    
    perfLogger.checkpoint('workflow_executed');

    // 保存用户消息和结果（无论成功或失败）
    if (result.session_id) {
      const userMessage: Message = {
        role: 'user',
        content: input,
        timestamp: new Date()
      };

      const assistantMessage: Message = {
        role: 'assistant',
        content: result.output || result.error || '工作流执行遇到问题',
        timestamp: new Date(),
        node_id: 'agent_d',
        metadata: {
          processing_time: result.metadata?.total_execution_time || 0,
          tokens_consumed: result.metadata?.total_tokens || 0
        }
      };

      try {
        await conversationService.addMessage(result.session_id, userMessage);
        await conversationService.addMessage(result.session_id, assistantMessage);
        logger.info('Session messages saved', { session_id: result.session_id });
      } catch (saveError: any) {
        logger.warn('Failed to save session messages', { 
          session_id: result.session_id, 
          error: saveError.message 
        });
      }
    }

    const executionTime = perfLogger.end({
      success: !result.error,
      session_id: result.session_id,
      output_length: result.output?.length || 0
    });

    logger.info('Workflow execution completed', {
      session_id: result.session_id,
      success: !result.error,
      api_execution_time: executionTime
    });

    res.json({
      success: !result.error,
      data: {
        session_id: result.session_id,
        output: result.output,
        execution_metadata: {
          total_execution_time: result.metadata?.total_execution_time || 0,
          agent_execution_times: {
            agent_a: result.metadata?.agent_a_execution_time || 0,
            agent_b: result.metadata?.agent_b_execution_time || 0,
            agent_c: result.metadata?.agent_c_execution_time || 0,
            agent_d: result.metadata?.agent_d_execution_time || 0
          },
          iteration_count: result.iteration_count,
          tokens_used: {
            agent_a: result.metadata?.agent_a_tokens_used || 0,
            agent_b: result.metadata?.agent_b_tokens_used || 0,
            agent_d: result.metadata?.agent_d_tokens_used || 0,
            total: (result.metadata?.agent_a_tokens_used || 0) + 
                   (result.metadata?.agent_b_tokens_used || 0) + 
                   (result.metadata?.agent_d_tokens_used || 0)
          }
        }
      },
      error: result.error,
      timestamp: new Date()
    } as ApiResponse);

  } catch (error: any) {
    const executionTime = perfLogger.end({
      success: false,
      error: error.message
    });

    logger.error('Workflow execution API error', {
      error: error.message,
      stack: error.stack,
      execution_time: executionTime
    });

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Workflow execution failed',
      timestamp: new Date()
    } as ApiResponse);
  }
});

// 获取会话历史
workflowRouter.get('/session/:session_id', async (req: Request, res: Response) => {
  try {
    const { error, value } = getSessionSchema.validate(req.params);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Invalid session ID',
        message: error.details[0].message,
        timestamp: new Date()
      } as ApiResponse);
    }

    const { session_id } = value;
    
    const conversation = await conversationService.getSessionHistory(session_id);
    
    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: 'Session not found',
        message: `Session ${session_id} does not exist`,
        timestamp: new Date()
      } as ApiResponse);
    }

    // 获取执行记录
    const executions = await executionService.getSessionExecutions(session_id);

    res.json({
      success: true,
      data: {
        conversation,
        executions,
        summary: {
          total_messages: conversation.messages.length,
          total_executions: executions.length,
          created_at: conversation.created_at,
          updated_at: conversation.updated_at,
          final_result: conversation.final_result
        }
      },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error: any) {
    logger.error('Get session API error', {
      error: error.message,
      session_id: req.params.session_id
    });

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to retrieve session',
      timestamp: new Date()
    } as ApiResponse);
  }
});

// 获取用户会话列表
workflowRouter.get('/sessions/user/:user_id', async (req: Request, res: Response) => {
  try {
    const userId = req.params.user_id;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = parseInt(req.query.offset as string) || 0;

    if (!userId || userId.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid user ID',
        timestamp: new Date()
      } as ApiResponse);
    }

    const sessions = await conversationService.getUserSessions(userId, limit, offset);

    res.json({
      success: true,
      data: {
        sessions,
        pagination: {
          limit,
          offset,
          total: sessions.length
        }
      },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error: any) {
    logger.error('Get user sessions API error', {
      error: error.message,
      user_id: req.params.user_id
    });

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to retrieve user sessions',
      timestamp: new Date()
    } as ApiResponse);
  }
});

// 搜索会话
workflowRouter.get('/sessions/search', async (req: Request, res: Response) => {
  try {
    const query = req.query.q as string;
    const userId = req.query.user_id as string;
    const limit = parseInt(req.query.limit as string) || 10;

    if (!query || query.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Search query is required',
        timestamp: new Date()
      } as ApiResponse);
    }

    const sessions = await conversationService.searchSessions(query, userId, limit);

    res.json({
      success: true,
      data: {
        sessions,
        search_metadata: {
          query,
          user_id: userId,
          results_count: sessions.length,
          limit
        }
      },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error: any) {
    logger.error('Search sessions API error', {
      error: error.message,
      query: req.query.q
    });

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Search failed',
      timestamp: new Date()
    } as ApiResponse);
  }
});

// 获取系统统计
workflowRouter.get('/stats', async (req: Request, res: Response) => {
  try {
    const hours = parseInt(req.query.hours as string) || 24;
    
    const systemStats = await executionService.getSystemStats(hours);
    const dbHealth = await healthService.checkHealth();

    res.json({
      success: true,
      data: {
        system_stats: systemStats,
        database_health: dbHealth,
        timestamp: new Date()
      },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error: any) {
    logger.error('Get stats API error', { error: error.message });

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to retrieve statistics',
      timestamp: new Date()
    } as ApiResponse);
  }
});

// 获取智能体性能统计
workflowRouter.get('/stats/agent/:node_id', async (req: Request, res: Response) => {
  try {
    const nodeId = req.params.node_id;
    const hours = parseInt(req.query.hours as string) || 24;

    if (!['agent_a', 'agent_b', 'agent_c', 'agent_d'].includes(nodeId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid node ID',
        message: 'Node ID must be one of: agent_a, agent_b, agent_c, agent_d',
        timestamp: new Date()
      } as ApiResponse);
    }

    const nodeStats = await executionService.getNodePerformanceStats(nodeId, hours);

    res.json({
      success: true,
      data: {
        node_id: nodeId,
        stats: nodeStats,
        period_hours: hours
      },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error: any) {
    logger.error('Get agent stats API error', {
      error: error.message,
      node_id: req.params.node_id
    });

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to retrieve agent statistics',
      timestamp: new Date()
    } as ApiResponse);
  }
});

// 健康检查
workflowRouter.get('/health', async (req: Request, res: Response) => {
  try {
    const dbHealth = await healthService.checkHealth();
    
    res.json({
      success: true,
      data: {
        status: 'healthy',
        database: dbHealth,
        timestamp: new Date(),
        version: '1.0.0'
      },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error: any) {
    logger.error('Health check API error', { error: error.message });

    res.status(503).json({
      success: false,
      error: 'Service unavailable',
      message: 'Health check failed',
      timestamp: new Date()
    } as ApiResponse);
  }
});

// 导出初始化函数
export { initializeWorkflow };
