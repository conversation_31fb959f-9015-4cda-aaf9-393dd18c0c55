import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { config, defaultAgentConfigs } from '../config';
import { NodeExecutionResult, AgentError } from '../types';
import { logger, PerformanceLogger } from '../utils/logger';

export class AgentB {
  private model: ChatOpenAI;
  private promptTemplate: PromptTemplate;
  private iterationPromptTemplate: PromptTemplate;
  private config: any;

  constructor() {
    this.config = defaultAgentConfigs.agent_b;
    
    // 初始化 ChatOpenAI 模型
    this.model = new ChatOpenAI({
      openAIApiKey: config.openai.apiKey,
      modelName: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      timeout: 60000,
    });

    // 初始执行的提示词模板
    this.promptTemplate = PromptTemplate.fromTemplate(`
${this.config.systemPrompt}

用户输入: {input}

这是你的初次分析。请提供全面的回答，但要考虑到你可能需要进行后续的迭代优化。

分析要求：
1. 提供初步的深入分析
2. 识别可能需要进一步探讨的方面
3. 给出明确的结论和建议
4. 为后续迭代留下改进空间

当前迭代: 第1次
初步分析：
`);

    // 迭代优化的提示词模板
    this.iterationPromptTemplate = PromptTemplate.fromTemplate(`
${this.config.systemPrompt}

原始用户输入: {input}
当前迭代次数: 第{iteration}次
上一次的分析结果: {previous_result}

基于上一次的分析，请进行进一步的思考和优化。考虑以下方面：
1. 是否有遗漏的重要观点？
2. 分析的深度是否足够？
3. 结论是否需要调整？
4. 是否有新的见解可以补充？

优化后的分析：
`);

    logger.info('Agent B initialized with iteration capability', { 
      model: this.config.model,
      temperature: this.config.temperature 
    });
  }

  async execute(input: string, sessionId: string, currentIteration: number = 0): Promise<NodeExecutionResult> {
    const perfLogger = new PerformanceLogger(`agent_b_execution_${sessionId}_iter_${currentIteration}`);
    const maxIterations = 3; // 最大迭代次数
    
    try {
      logger.debug('Agent B starting execution', { 
        session_id: sessionId,
        input_length: input.length,
        current_iteration: currentIteration 
      });

      // 验证输入
      if (!input || input.trim().length === 0) {
        throw new AgentError('Input cannot be empty', 'INVALID_INPUT', 'agent_b');
      }

      let result: string;
      let totalTokens = 0;

      if (currentIteration === 0) {
        // 首次执行
        result = await this.performInitialAnalysis(input, perfLogger);
        totalTokens += this.estimateTokens(input, result);
      } else {
        // 迭代执行 - 需要获取上一次的结果
        result = await this.performIterativeAnalysis(input, currentIteration, sessionId, perfLogger);
        totalTokens += this.estimateTokens(input, result);
      }

      // 决定是否需要继续迭代
      const shouldContinue = this.shouldContinueIteration(result, currentIteration, maxIterations);
      
      if (shouldContinue && currentIteration < maxIterations - 1) {
        logger.info('Agent B will continue iteration', {
          session_id: sessionId,
          current_iteration: currentIteration,
          next_iteration: currentIteration + 1
        });
        
        // 递归调用进行下一次迭代
        const nextResult = await this.execute(input, sessionId, currentIteration + 1);
        
        return {
          success: true,
          result: this.combineIterationResults(result, nextResult.result, currentIteration + 1),
          execution_time: perfLogger.end(),
          tokens_used: totalTokens + (nextResult.tokens_used || 0)
        };
      } else {
        // 完成所有迭代
        const executionTime = perfLogger.end({
          total_iterations: currentIteration + 1,
          final_result_length: result.length,
          success: true
        });

        logger.info('Agent B execution completed', {
          session_id: sessionId,
          total_iterations: currentIteration + 1,
          execution_time: executionTime,
          final_result_length: result.length
        });

        return {
          success: true,
          result: this.formatFinalResult(result, currentIteration + 1),
          execution_time: executionTime,
          tokens_used: totalTokens
        };
      }

    } catch (error: any) {
      const executionTime = perfLogger.end({
        success: false,
        error: error.message,
        iteration: currentIteration
      });

      logger.error('Agent B execution failed', {
        session_id: sessionId,
        iteration: currentIteration,
        error: error.message,
        execution_time: executionTime,
        stack: error.stack
      });

      if (error instanceof AgentError) {
        throw error;
      }

      throw new AgentError(
        `Agent B execution failed at iteration ${currentIteration}: ${error.message}`,
        'EXECUTION_FAILED',
        'agent_b',
        { iteration: currentIteration, originalError: error.message }
      );
    }
  }

  private async performInitialAnalysis(input: string, perfLogger: PerformanceLogger): Promise<string> {
    const prompt = await this.promptTemplate.format({ input });
    perfLogger.checkpoint('initial_prompt_formatted');
    
    const response = await this.model.invoke(prompt);
    perfLogger.checkpoint('initial_model_invoked');
    
    return this.processResponse(response.content);
  }

  private async performIterativeAnalysis(
    input: string, 
    iteration: number, 
    sessionId: string, 
    perfLogger: PerformanceLogger
  ): Promise<string> {
    // 这里简化处理，在实际应用中可能需要从数据库获取上一次的结果
    const previousResult = `[上一次迭代的结果 - 迭代${iteration}]`;
    
    const prompt = await this.iterationPromptTemplate.format({ 
      input, 
      iteration: iteration + 1,
      previous_result: previousResult 
    });
    
    perfLogger.checkpoint(`iteration_${iteration}_prompt_formatted`);
    
    const response = await this.model.invoke(prompt);
    perfLogger.checkpoint(`iteration_${iteration}_model_invoked`);
    
    return this.processResponse(response.content);
  }

  private shouldContinueIteration(result: string, currentIteration: number, maxIterations: number): boolean {
    // 简单的继续条件判断
    if (currentIteration >= maxIterations - 1) {
      return false;
    }

    // 如果结果太短，可能需要更深入的分析
    if (result.length < 200) {
      return true;
    }

    // 基于关键词判断是否需要进一步分析
    const analysisKeywords = ['需要进一步', '可以深入', '待补充', '有待完善'];
    const hasAnalysisHints = analysisKeywords.some(keyword => result.includes(keyword));
    
    return hasAnalysisHints;
  }

  private combineIterationResults(currentResult: string, nextResult: string, totalIterations: number): string {
    return `=== Agent B 多轮分析结果 (共${totalIterations}轮) ===

【第${totalIterations - 1}轮分析】
${currentResult}

【第${totalIterations}轮深化分析】
${nextResult}

=== 分析完成 ===`;
  }

  private formatFinalResult(result: string, totalIterations: number): string {
    return `=== Agent B 最终分析结果 (共${totalIterations}轮迭代) ===

${result}

=== 迭代分析完成 ===`;
  }

  private processResponse(content: any): string {
    if (typeof content === 'string') {
      return content.trim();
    }
    
    if (content && typeof content === 'object') {
      return JSON.stringify(content);
    }
    
    return String(content || '').trim();
  }

  private estimateTokens(input: string, output: string): number {
    const inputTokens = Math.ceil(input.length / 4);
    const outputTokens = Math.ceil(output.length / 4);
    return inputTokens + outputTokens;
  }

  // 健康检查方法
  async healthCheck(): Promise<boolean> {
    try {
      const testInput = "健康检查测试";
      const response = await this.model.invoke("请回复'Agent B健康检查通过'");
      return response && response.content !== null;
    } catch (error: any) {
      logger.warn('Agent B health check failed', { error: error.message });
      return false;
    }
  }

  // 获取配置信息
  getConfig(): any {
    return {
      name: this.config.name,
      description: this.config.description,
      model: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      supports_iteration: true,
      max_iterations: 3
    };
  }
}
