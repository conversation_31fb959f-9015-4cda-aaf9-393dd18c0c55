import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { config, defaultAgentConfigs } from '../config';
import { NodeExecutionResult, AgentError } from '../types';
import { logger, PerformanceLogger } from '../utils/logger';

export class AgentA {
  private model: ChatOpenAI;
  private promptTemplate: PromptTemplate;
  private config: any;

  constructor() {
    this.config = defaultAgentConfigs.agent_a;
    
    // 初始化 ChatOpenAI 模型
    this.model = new ChatOpenAI({
      openAIApiKey: config.openai.apiKey,
      modelName: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      timeout: 60000,
    });

    // 创建提示词模板
    this.promptTemplate = PromptTemplate.fromTemplate(`
${this.config.systemPrompt}

用户输入: {input}

请仔细分析用户的输入，提供深入的分析和见解。你的回答将作为后续智能体的重要输入，请确保内容准确、详细且有价值。

输出要求：
1. 对输入内容进行深入分析
2. 提取关键信息点
3. 提供专业见解
4. 保持回答的逻辑性和条理性

分析结果：
`);

    logger.info('Agent A initialized', { 
      model: this.config.model,
      temperature: this.config.temperature 
    });
  }

  async execute(input: string, sessionId: string): Promise<NodeExecutionResult> {
    const perfLogger = new PerformanceLogger(`agent_a_execution_${sessionId}`);
    
    try {
      logger.debug('Agent A starting execution', { 
        session_id: sessionId,
        input_length: input.length 
      });

      // 验证输入
      if (!input || input.trim().length === 0) {
        throw new AgentError('Input cannot be empty', 'INVALID_INPUT', 'agent_a');
      }

      // 构建提示词
      const prompt = await this.promptTemplate.format({ input });
      
      perfLogger.checkpoint('prompt_formatted');

      // 调用模型
      const response = await this.model.invoke(prompt);
      
      perfLogger.checkpoint('model_invoked');

      // 处理响应
      const result = this.processResponse(response.content);
      
      const executionTime = perfLogger.end({
        input_length: input.length,
        output_length: result.length,
        success: true
      });

      logger.info('Agent A execution completed', {
        session_id: sessionId,
        execution_time: executionTime,
        input_length: input.length,
        output_length: result.length
      });

      return {
        success: true,
        result,
        execution_time: executionTime,
        tokens_used: this.estimateTokens(input, result)
      };

    } catch (error: any) {
      const executionTime = perfLogger.end({
        success: false,
        error: error.message
      });

      logger.error('Agent A execution failed', {
        session_id: sessionId,
        error: error.message,
        execution_time: executionTime,
        stack: error.stack
      });

      if (error instanceof AgentError) {
        throw error;
      }

      throw new AgentError(
        `Agent A execution failed: ${error.message}`,
        'EXECUTION_FAILED',
        'agent_a',
        { originalError: error.message }
      );
    }
  }

  private processResponse(content: any): string {
    if (typeof content === 'string') {
      return content.trim();
    }
    
    if (content && typeof content === 'object') {
      // 处理可能的结构化响应
      return JSON.stringify(content);
    }
    
    return String(content || '').trim();
  }

  private estimateTokens(input: string, output: string): number {
    // 简单的token估算：大约4个字符=1个token
    const inputTokens = Math.ceil(input.length / 4);
    const outputTokens = Math.ceil(output.length / 4);
    return inputTokens + outputTokens;
  }

  // 健康检查方法
  async healthCheck(): Promise<boolean> {
    try {
      const testInput = "健康检查测试";
      const response = await this.model.invoke("请回复'健康检查通过'");
      return response && response.content !== null;
    } catch (error: any) {
      logger.warn('Agent A health check failed', { error: error.message });
      return false;
    }
  }

  // 获取配置信息
  getConfig(): any {
    return {
      name: this.config.name,
      description: this.config.description,
      model: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens
    };
  }
}
