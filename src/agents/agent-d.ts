import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { config, defaultAgentConfigs } from '../config';
import { NodeExecutionResult, AgentError } from '../types';
import { logger, PerformanceLogger } from '../utils/logger';

export class AgentD {
  private model: ChatOpenAI;
  private promptTemplate: PromptTemplate;
  private config: any;

  constructor() {
    this.config = defaultAgentConfigs.agent_d;
    
    // 初始化 ChatOpenAI 模型
    this.model = new ChatOpenAI({
      openAIApiKey: config.openai.apiKey,
      modelName: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      timeout: 120000,
    });

    // 创建汇聚分析的提示词模板
    this.promptTemplate = PromptTemplate.fromTemplate(`
${this.config.systemPrompt}

你现在需要整合以下来自不同智能体的分析结果：

=== 原始用户输入 ===
{original_input}

=== Agent A 的分析结果 ===
{agent_a_result}

=== Agent B 的迭代分析结果 ===
{agent_b_result}

=== Agent C 的函数处理结果 ===
{agent_c_result}

=== 整合任务 ===
请基于以上所有信息，提供一个完整、准确、有价值的最终回答。你的整合需要：

1. **信息综合**: 将所有智能体的输出进行有机整合
2. **逻辑梳理**: 确保最终答案逻辑清晰、条理分明
3. **价值提取**: 突出最有价值的见解和结论
4. **完整性检查**: 确保回答完整地解决了用户的原始问题
5. **质量保证**: 提供高质量、专业的最终答案

=== 最终整合分析 ===
`);

    logger.info('Agent D initialized as aggregation agent', { 
      model: this.config.model,
      temperature: this.config.temperature 
    });
  }

  async execute(combinedInput: any, sessionId: string): Promise<NodeExecutionResult> {
    const perfLogger = new PerformanceLogger(`agent_d_execution_${sessionId}`);
    
    try {
      logger.debug('Agent D starting execution', { 
        session_id: sessionId,
        has_original_input: !!combinedInput.original_input,
        has_agent_a_result: !!combinedInput.agent_a_result,
        has_agent_b_result: !!combinedInput.agent_b_result,
        has_agent_c_result: !!combinedInput.agent_c_result
      });

      // 验证所有必需的输入
      this.validateInputs(combinedInput);
      
      perfLogger.checkpoint('inputs_validated');

      // 预处理输入数据
      const processedInputs = this.preprocessInputs(combinedInput);
      
      perfLogger.checkpoint('inputs_preprocessed');

      // 构建最终提示词
      const prompt = await this.promptTemplate.format(processedInputs);
      
      perfLogger.checkpoint('prompt_formatted');

      // 调用模型进行最终整合
      const response = await this.model.invoke(prompt);
      
      perfLogger.checkpoint('model_invoked');

      // 处理和增强响应
      const result = this.enhanceResponse(response.content, combinedInput);
      
      const executionTime = perfLogger.end({
        input_agents_count: 3,
        output_length: result.length,
        success: true
      });

      logger.info('Agent D execution completed', {
        session_id: sessionId,
        execution_time: executionTime,
        output_length: result.length,
        aggregated_agents: ['agent_a', 'agent_b', 'agent_c']
      });

      return {
        success: true,
        result,
        execution_time: executionTime,
        tokens_used: this.estimateTokens(JSON.stringify(combinedInput), result)
      };

    } catch (error: any) {
      const executionTime = perfLogger.end({
        success: false,
        error: error.message
      });

      logger.error('Agent D execution failed', {
        session_id: sessionId,
        error: error.message,
        execution_time: executionTime,
        stack: error.stack
      });

      if (error instanceof AgentError) {
        throw error;
      }

      throw new AgentError(
        `Agent D execution failed: ${error.message}`,
        'EXECUTION_FAILED',
        'agent_d',
        { originalError: error.message }
      );
    }
  }

  private validateInputs(combinedInput: any): void {
    const requiredFields = ['original_input', 'agent_a_result', 'agent_b_result', 'agent_c_result'];
    const missingFields = requiredFields.filter(field => !combinedInput[field]);
    
    if (missingFields.length > 0) {
      throw new AgentError(
        `Missing required inputs: ${missingFields.join(', ')}`,
        'MISSING_INPUTS',
        'agent_d',
        { missingFields }
      );
    }

    // 验证输入内容不为空
    for (const field of requiredFields) {
      if (typeof combinedInput[field] === 'string' && combinedInput[field].trim().length === 0) {
        throw new AgentError(
          `Input field ${field} cannot be empty`,
          'EMPTY_INPUT',
          'agent_d',
          { field }
        );
      }
    }
  }

  private preprocessInputs(combinedInput: any): any {
    try {
      return {
        original_input: this.sanitizeInput(combinedInput.original_input),
        agent_a_result: this.sanitizeInput(combinedInput.agent_a_result),
        agent_b_result: this.sanitizeInput(combinedInput.agent_b_result),
        agent_c_result: this.sanitizeInput(combinedInput.agent_c_result)
      };
    } catch (error: any) {
      throw new AgentError(
        `Input preprocessing failed: ${error.message}`,
        'PREPROCESSING_FAILED',
        'agent_d',
        { originalError: error.message }
      );
    }
  }

  private sanitizeInput(input: any): string {
    if (typeof input === 'string') {
      return input.trim();
    }
    
    if (typeof input === 'object') {
      return JSON.stringify(input, null, 2);
    }
    
    return String(input || '').trim();
  }

  private enhanceResponse(content: any, combinedInput: any): string {
    try {
      const baseResponse = this.processResponse(content);
      
      // 添加执行摘要
      const executionSummary = this.generateExecutionSummary(combinedInput);
      
      // 添加质量指标
      const qualityMetrics = this.generateQualityMetrics(baseResponse, combinedInput);
      
      const enhancedResponse = `${baseResponse}

${executionSummary}

${qualityMetrics}`;

      return enhancedResponse;
    } catch (error: any) {
      logger.warn('Response enhancement failed, returning base response', { error: error.message });
      return this.processResponse(content);
    }
  }

  private processResponse(content: any): string {
    if (typeof content === 'string') {
      return content.trim();
    }
    
    if (content && typeof content === 'object') {
      return JSON.stringify(content);
    }
    
    return String(content || '').trim();
  }

  private generateExecutionSummary(combinedInput: any): string {
    const timestamp = new Date().toISOString();
    
    return `
=== 多智能体协作执行摘要 ===
• 执行时间: ${timestamp}
• 协作智能体: Agent A (深度分析) + Agent B (迭代优化) + Agent C (函数处理) + Agent D (结果整合)
• 原始输入长度: ${combinedInput.original_input?.length || 0} 字符
• Agent A 分析长度: ${combinedInput.agent_a_result?.length || 0} 字符
• Agent B 分析长度: ${combinedInput.agent_b_result?.length || 0} 字符
• Agent C 处理长度: ${combinedInput.agent_c_result?.length || 0} 字符
• 整合方式: 多维度信息融合与逻辑重构`;
  }

  private generateQualityMetrics(response: string, combinedInput: any): string {
    const wordCount = response.split(/\s+/).length;
    const hasStructure = response.includes('===') || response.includes('###') || response.includes('**');
    const referencesAllAgents = ['Agent A', 'Agent B', 'Agent C'].every(agent => 
      response.includes(agent) || response.includes(agent.toLowerCase())
    );
    
    return `
=== 回答质量指标 ===
• 回答长度: ${response.length} 字符 (约 ${wordCount} 词)
• 结构化程度: ${hasStructure ? '高' : '中等'}
• 信息整合度: ${referencesAllAgents ? '完整整合所有智能体结果' : '部分整合'}
• 响应完整性: ✓ 已整合多智能体分析结果
• 逻辑连贯性: ✓ 经过Agent D逻辑重构与优化
=== 协作完成 ===`;
  }

  private estimateTokens(input: string, output: string): number {
    const inputTokens = Math.ceil(input.length / 4);
    const outputTokens = Math.ceil(output.length / 4);
    return inputTokens + outputTokens;
  }

  // 健康检查方法
  async healthCheck(): Promise<boolean> {
    try {
      const testCombinedInput = {
        original_input: "健康检查测试",
        agent_a_result: "Agent A 分析结果",
        agent_b_result: "Agent B 迭代结果", 
        agent_c_result: "Agent C 函数结果"
      };
      
      const response = await this.model.invoke("请回复'Agent D健康检查通过'");
      return response && response.content !== null;
    } catch (error: any) {
      logger.warn('Agent D health check failed', { error: error.message });
      return false;
    }
  }

  // 获取配置信息
  getConfig(): any {
    return {
      name: this.config.name,
      description: this.config.description,
      model: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      role: 'aggregation',
      input_sources: ['agent_a', 'agent_b', 'agent_c'],
      output_type: 'final_response'
    };
  }

  // 分析输入复杂度
  analyzeInputComplexity(combinedInput: any): any {
    try {
      const complexity = {
        total_input_length: 0,
        agent_contributions: {},
        estimated_processing_time: 0
      };

      // 计算各智能体贡献
      ['agent_a_result', 'agent_b_result', 'agent_c_result'].forEach(field => {
        const length = combinedInput[field]?.length || 0;
        complexity.total_input_length += length;
        (complexity.agent_contributions as any)[field] = {
          length,
          percentage: 0 // 稍后计算
        };
      });

      // 计算百分比
      Object.keys(complexity.agent_contributions).forEach(field => {
        const contrib = (complexity.agent_contributions as any)[field];
        contrib.percentage = complexity.total_input_length > 0 
          ? Math.round((contrib.length / complexity.total_input_length) * 100)
          : 0;
      });

      // 估算处理时间（基于输入复杂度）
      complexity.estimated_processing_time = Math.max(2000, complexity.total_input_length * 0.1);

      return complexity;
    } catch (error: any) {
      logger.warn('Input complexity analysis failed', { error: error.message });
      return { error: 'Analysis failed' };
    }
  }
}
