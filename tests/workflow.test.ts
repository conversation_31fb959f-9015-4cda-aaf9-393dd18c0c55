import { SimpleMultiAgentWorkflow } from '../src/agents/simple-workflow';
import { AgentA } from '../src/agents/agent-a';
import { AgentB } from '../src/agents/agent-b';
import { AgentC } from '../src/agents/agent-c';
import { AgentD } from '../src/agents/agent-d';
import { DatabaseConnection } from '../src/database/connection';
import { config } from '../src/config';

// 模拟测试环境
const TEST_TIMEOUT = 60000; // 60秒超时

describe('Multi-Agent Workflow Tests', () => {
  let workflow: SimpleMultiAgentWorkflow;
  let db: DatabaseConnection;

  beforeAll(async () => {
    // 设置测试环境
    process.env.NODE_ENV = 'test';
    process.env.MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/langgraph_test';
    
    // 初始化数据库连接
    db = DatabaseConnection.getInstance();
    await db.connect();

    // 初始化工作流
    workflow = new SimpleMultiAgentWorkflow();
    await workflow.compile();
  }, TEST_TIMEOUT);

  afterAll(async () => {
    // 清理测试环境
    await db.disconnect();
  });

  describe('Individual Agent Tests', () => {
    test('Agent A should process input correctly', async () => {
      const agentA = new AgentA();
      const testInput = '请分析人工智能的发展趋势';
      
      const result = await agentA.execute(testInput, 'test-session-a');
      
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      expect(typeof result.result).toBe('string');
      expect(result.result.length).toBeGreaterThan(0);
      expect(result.execution_time).toBeGreaterThan(0);
      expect(result.tokens_used).toBeGreaterThan(0);
    }, TEST_TIMEOUT);

    test('Agent B should support iteration', async () => {
      const agentB = new AgentB();
      const testInput = '分析区块链技术的应用前景';
      
      const result = await agentB.execute(testInput, 'test-session-b', 0);
      
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      expect(result.result).toContain('迭代');
      expect(result.execution_time).toBeGreaterThan(0);
    }, TEST_TIMEOUT);

    test('Agent C should execute function correctly', async () => {
      const agentC = new AgentC();
      const testInput = '测试输入内容';
      
      const result = await agentC.execute(testInput, 'test-session-c');
      
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      expect(result.result).toContain(testInput);
      expect(result.result).toContain('xxxxdx');
      expect(result.execution_time).toBeGreaterThan(0);
      expect(result.tokens_used).toBe(0); // 函数执行不消耗tokens
    });

    test('Agent D should aggregate results correctly', async () => {
      const agentD = new AgentD();
      const testInput = {
        original_input: '测试输入',
        agent_a_result: 'Agent A 的分析结果',
        agent_b_result: 'Agent B 的迭代结果',
        agent_c_result: 'Agent C 的函数结果'
      };
      
      const result = await agentD.execute(testInput, 'test-session-d');
      
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      expect(result.result).toContain('整合');
      expect(result.execution_time).toBeGreaterThan(0);
      expect(result.tokens_used).toBeGreaterThan(0);
    }, TEST_TIMEOUT);
  });

  describe('Agent Health Checks', () => {
    test('All agents should pass health check', async () => {
      const agentA = new AgentA();
      const agentB = new AgentB();
      const agentC = new AgentC();
      const agentD = new AgentD();

      const healthResults = await Promise.all([
        agentA.healthCheck(),
        agentB.healthCheck(),
        agentC.healthCheck(),
        agentD.healthCheck()
      ]);

      expect(healthResults.every(health => health === true)).toBe(true);
    }, TEST_TIMEOUT);
  });

  describe('Workflow Integration Tests', () => {
    test('Complete workflow should execute successfully', async () => {
      const testInput = '请分析云计算技术的发展现状和未来趋势';
      
      const result = await workflow.execute(testInput, 'test-user-1');
      
      expect(result).toBeDefined();
      expect(result.session_id).toBeDefined();
      expect(result.output).toBeDefined();
      expect(result.output.length).toBeGreaterThan(0);
      expect(result.error).toBeUndefined();
      
      // 验证所有智能体都有执行结果
      expect(result.nodeA_result).toBeDefined();
      expect(result.nodeB_result).toBeDefined();
      expect(result.nodeC_result).toBeDefined();
      expect(result.nodeD_result).toBeDefined();
      
      // 验证元数据
      expect(result.metadata).toBeDefined();
      expect(result.metadata.workflow_version).toBe('1.0');
      expect(result.metadata.start_time).toBeDefined();
      expect(result.metadata.end_time).toBeDefined();
    }, TEST_TIMEOUT);

    test('Workflow should handle empty input gracefully', async () => {
      try {
        await workflow.execute('', 'test-user-2');
        fail('Should have thrown an error for empty input');
      } catch (error) {
        expect(error.message).toContain('empty');
      }
    });

    test('Workflow should handle very long input', async () => {
      const longInput = 'A'.repeat(5000) + ' 请分析这个长文本';
      
      const result = await workflow.execute(longInput, 'test-user-3');
      
      expect(result).toBeDefined();
      expect(result.session_id).toBeDefined();
      expect(result.output).toBeDefined();
      expect(result.error).toBeUndefined();
    }, TEST_TIMEOUT);
  });

  describe('Database Integration Tests', () => {
    test('Session should be saved to database', async () => {
      const testInput = '数据库测试输入';
      
      const result = await workflow.execute(testInput, 'test-user-db');
      
      expect(result.session_id).toBeDefined();
      
      // 验证会话已保存到数据库
      const conversationsCollection = db.getConversationsCollection();
      const savedSession = await conversationsCollection.findOne({
        session_id: result.session_id
      });
      
      expect(savedSession).toBeDefined();
      expect(savedSession.session_id).toBe(result.session_id);
      expect(savedSession.final_result).toBeDefined();
      expect(savedSession.final_result.success).toBe(true);
    }, TEST_TIMEOUT);

    test('Agent executions should be recorded', async () => {
      const testInput = '智能体执行记录测试';
      
      const result = await workflow.execute(testInput, 'test-user-exec');
      
      // 验证执行记录已保存
      const executionsCollection = db.getAgentExecutionsCollection();
      const executions = await executionsCollection.find({
        session_id: result.session_id
      }).toArray();
      
      expect(executions.length).toBeGreaterThan(0);
      
      // 验证包含所有智能体的执行记录
      const nodeIds = executions.map(exec => exec.node_id);
      expect(nodeIds).toContain('agent_a');
      expect(nodeIds).toContain('agent_b');
      expect(nodeIds).toContain('agent_c');
      expect(nodeIds).toContain('agent_d');
    }, TEST_TIMEOUT);
  });

  describe('Error Handling Tests', () => {
    test('Should handle invalid API key gracefully', async () => {
      // 暂时修改API key
      const originalKey = process.env.OPENAI_API_KEY;
      process.env.OPENAI_API_KEY = 'invalid-key';
      
      try {
        const agentA = new AgentA();
        await agentA.execute('测试输入', 'test-session-error');
        fail('Should have thrown an error with invalid API key');
      } catch (error) {
        expect(error).toBeDefined();
        expect(error.message).toContain('failed');
      } finally {
        // 恢复原API key
        process.env.OPENAI_API_KEY = originalKey;
      }
    });

    test('Should handle database connection issues', async () => {
      // 这个测试需要在实际环境中手动断开数据库连接来测试
      const isHealthy = await db.healthCheck();
      expect(typeof isHealthy).toBe('boolean');
    });
  });

  describe('Performance Tests', () => {
    test('Workflow execution should complete within reasonable time', async () => {
      const startTime = Date.now();
      const testInput = '性能测试输入';
      
      const result = await workflow.execute(testInput, 'test-user-perf');
      
      const executionTime = Date.now() - startTime;
      
      expect(result).toBeDefined();
      expect(executionTime).toBeLessThan(30000); // 应在30秒内完成
      
      console.log(`Workflow execution time: ${executionTime}ms`);
    }, TEST_TIMEOUT);

    test('Agent C should be fastest (function execution)', async () => {
      const agentC = new AgentC();
      const startTime = Date.now();
      
      const result = await agentC.execute('性能测试', 'perf-test-c');
      
      const executionTime = Date.now() - startTime;
      
      expect(result.success).toBe(true);
      expect(executionTime).toBeLessThan(100); // 应在100ms内完成
      
      console.log(`Agent C execution time: ${executionTime}ms`);
    });
  });
});
