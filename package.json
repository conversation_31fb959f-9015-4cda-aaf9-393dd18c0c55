{"name": "ht-langchain-mvp-v2", "version": "1.0.0", "description": "基于LangChain.js和LangGraph的多智能体系统MVP", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["langchain", "langgraph", "multi-agent", "mongodb", "typescript"], "author": "HT Team", "license": "MIT", "dependencies": {"@langchain/core": "^0.2.31", "@langchain/openai": "^0.2.8", "@langchain/community": "^0.2.31", "@langchain/langgraph": "^0.0.34", "langchain": "^0.2.18", "mongodb": "^6.3.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "winston": "^3.11.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "joi": "^17.11.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "prettier": "^3.1.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}