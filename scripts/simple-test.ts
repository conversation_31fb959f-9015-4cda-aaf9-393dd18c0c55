#!/usr/bin/env ts-node

// 简单的智能体功能测试（不需要外部依赖）

import { AgentC } from '../src/agents/agent-c';
import { logger } from '../src/utils/logger';

async function testAgentC() {
  console.log('🧪 测试 Agent C (函数智能体)...');
  
  try {
    const agentC = new AgentC();
    const testInput = '测试输入内容';
    
    console.log(`📝 输入: ${testInput}`);
    
    const result = await agentC.execute(testInput, 'test-session');
    
    if (result.success) {
      console.log('✅ Agent C 执行成功');
      console.log(`📊 执行时间: ${result.execution_time}ms`);
      console.log(`📄 输出预览: ${result.result?.substring(0, 200)}...`);
    } else {
      console.log('❌ Agent C 执行失败');
      console.log(`错误: ${result.error}`);
    }
    
  } catch (error: any) {
    console.log('❌ 测试过程中发生错误:', error.message);
  }
}

async function testHealthCheck() {
  console.log('\n🏥 测试 Agent C 健康检查...');
  
  try {
    const agentC = new AgentC();
    const isHealthy = await agentC.healthCheck();
    
    if (isHealthy) {
      console.log('✅ Agent C 健康检查通过');
    } else {
      console.log('⚠️ Agent C 健康检查失败');
    }
    
  } catch (error: any) {
    console.log('❌ 健康检查过程中发生错误:', error.message);
  }
}

async function testConfig() {
  console.log('\n⚙️ 测试配置信息...');
  
  try {
    const agentC = new AgentC();
    const config = agentC.getConfig();
    
    console.log('📋 Agent C 配置信息:');
    console.log(JSON.stringify(config, null, 2));
    
  } catch (error: any) {
    console.log('❌ 获取配置信息失败:', error.message);
  }
}

async function main() {
  console.log('🚀 开始简单功能测试...\n');
  
  await testAgentC();
  await testHealthCheck();
  await testConfig();
  
  console.log('\n🎉 测试完成！');
  console.log('Agent C (函数智能体) 基础功能正常。');
  console.log('\n📝 说明：');
  console.log('- Agent C 是函数智能体，执行 codeA + "xxxxdx" 操作');
  console.log('- 不需要 OpenAI API Key，因为它不调用 LLM');
  console.log('- 可以独立验证基础架构的正确性');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}
