# HT LangChain Multi-Agent System MVP v2 - LangGraph Edition

基于LangChain.js和LangGraph的现代化多智能体系统，支持动态路由、工具集成、并行执行和智能质量控制。

LangChain.js和LangGraph相关文档：
1. https://zread.ai/langchain-ai/langgraphjs
2. https://langchain-ai.github.io/langgraphjs/

## ✨ LangGraph 版本特性

🎉 **全新的基于LangGraph的多智能体架构**

- ✅ 智能分析器和动态路由系统
- ✅ 基于条件的智能体选择和执行
- ✅ 丰富的工具集成支持（6类专业工具）
- ✅ 优化的并行执行和资源管理
- ✅ 多层质量保证和性能监控
- ✅ 完全向后兼容原有系统

## 🏗️ LangGraph 架构

### 工作流程图
```
用户输入 → Analyzer → Router → [AgentA, AgentB] → AgentC → AgentD → Finalizer → 输出
          (分析)     (路由)      (并行执行)       (依赖A)   (聚合)   (终结)
```

### 核心组件

#### 1. Analyzer（分析器）
- **功能**: 分析输入复杂度、类型和关键信息
- **输出**: 复杂度等级、内容类型、关键词提取
- **工具**: TextAnalyzer

#### 2. Router（路由器）
- **功能**: 基于分析结果的智能路由决策
- **策略**: 
  - 高复杂度 → 所有智能体协作
  - 计算类型 → 分析+函数处理
  - 标准任务 → 分析+迭代优化

#### 3. 增强智能体

| 智能体 | 核心能力 | 集成工具 | 执行模式 |
|-------|---------|----------|----------|
| **Agent A** | 深度分析+知识检索 | KnowledgeRetriever | 并行 |
| **Agent B** | 迭代优化+质量控制 | IterationController, QualityChecker | 并行 |
| **Agent C** | 计算处理+数据分析 | Calculator, DataProcessor | 依赖A |
| **Agent D** | 智能聚合+质量保证 | ContentAggregator, QualityChecker | 聚合 |

#### 4. Finalizer（终结器）
- **功能**: 性能统计、质量评估、结果优化
- **输出**: 完整的执行报告和性能指标

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- MongoDB >= 4.4
- OpenAI API访问权限

### 安装配置

1. **克隆并安装**
```bash
git clone <project-url>
cd ht-langchain-mvp-v2
npm install
```

2. **环境配置**
```bash
cp env.template .env
# 编辑 .env 文件
```

3. **启动服务**
```bash
npm run dev
```

### 验证安装
```bash
# 测试LangGraph工作流
npx ts-node scripts/test-langgraph.ts
```

## 📝 API 接口

### LangGraph工作流执行（推荐）

**POST** `/api/langgraph/execute`

```json
{
  "input": "请分析人工智能在未来十年的发展趋势",
  "user_id": "user123"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "session_id": "uuid-string",
    "output": "基于多智能体协作的综合分析结果...",
    "analysis_result": {
      "complexity": "medium",
      "type": "analytical",
      "keywords": ["人工智能", "发展", "趋势"],
      "confidence": 0.85
    },
    "route_decision": {
      "agents_to_execute": ["agent_a", "agent_b"],
      "execution_mode": "parallel",
      "reasoning": "标准任务，使用分析和迭代智能体"
    },
    "agents_executed": {
      "agent_a": true,
      "agent_b": true,
      "agent_c": false,
      "agent_d": true
    },
    "execution_metadata": {
      "total_execution_time": 3500,
      "workflow_completed": true,
      "performance_metrics": {
        "total_tokens_used": 2450,
        "total_tool_calls": 8,
        "average_response_time": 3500
      }
    },
    "agent_results": {
      "agent_a": {
        "success": true,
        "confidence": 0.85,
        "execution_time": 1200,
        "tokens_used": 800,
        "tools_used": ["knowledge_retriever"],
        "content_length": 1250
      },
      "agent_b": {
        "success": true,
        "confidence": 0.80,
        "execution_time": 1800,
        "tokens_used": 1200,
        "tools_used": ["iteration_controller", "quality_checker"],
        "content_length": 1500,
        "iterations": 2
      },
      "agent_d": {
        "success": true,
        "confidence": 0.88,
        "execution_time": 500,
        "tokens_used": 450,
        "tools_used": ["content_aggregator", "quality_checker"],
        "content_length": 2100,
        "quality_scores": {
          "completeness": 0.9,
          "accuracy": 0.85,
          "clarity": 0.8
        }
      }
    }
  }
}
```

### 其他接口

- **GET** `/api/langgraph/status` - 获取工作流状态信息
- **GET** `/api/langgraph/health` - LangGraph健康检查
- **POST** `/api/workflow/execute` - 简单工作流（向后兼容）

## 🛠️ 技术栈

### 核心依赖
```json
{
  "@langchain/langgraph": "^0.3.8",
  "@langchain/core": "^0.3.65", 
  "@langchain/openai": "^0.5.10",
  "@langchain/community": "^0.3.47",
  "langchain": "^0.3.26",
  "zod": "^3.25.32"
}
```

### 工具系统
1. **TextAnalyzer** - 文本分析和复杂度评估
2. **KnowledgeRetriever** - 知识库检索
3. **Calculator** - 数学计算和数据处理  
4. **ContentAggregator** - 内容聚合
5. **QualityChecker** - 质量检查和评估
6. **IterationController** - 迭代控制

## 📊 性能特性

### 智能路由示例

```typescript
// 高复杂度任务
if (complexity === 'high') {
  route = ['agent_a', 'agent_b', 'agent_c']; // 全体协作
}

// 计算类任务  
if (type === 'computational') {
  route = ['agent_a', 'agent_c']; // 分析+计算
}

// 标准任务
else {
  route = ['agent_a', 'agent_b']; // 分析+迭代
}
```

### 并行执行优化
- Agent A 和 Agent B 并行执行，减少总体延迟
- Agent C 依赖 Agent A 结果，确保数据一致性
- Agent D 聚合所有结果，保证完整性

### 质量保证机制
```typescript
// 多维度质量评估
const qualityMetrics = {
  completeness: 0.9,  // 完整性
  accuracy: 0.85,     // 准确性
  clarity: 0.8        // 清晰度
};
```

## 🧪 测试与验证

### 功能测试
```bash
# 运行LangGraph测试套件
npx ts-node scripts/test-langgraph.ts
```

### 性能基准测试
测试结果示例：
- ✅ 成功率: 100% (3/3)
- ⏱️ 平均执行时间: 3200ms
- 🪙 平均Token使用: 2300
- 📝 平均输出长度: 1800字符

### 测试用例
1. **简单文本分析** - 复杂度: medium
2. **计算类任务** - 复杂度: high，路由到Agent A+C
3. **创意性任务** - 复杂度: medium，并行执行
4. **技术性问题** - 复杂度: high，全体协作

## 🔧 开发指南

### 项目结构
```
src/
├── workflows/        # LangGraph工作流实现
│   └── langgraph-workflow.ts
├── tools/           # 工具系统
│   └── index.ts
├── routes/          # API路由
│   ├── workflow.ts  # 原有路由（兼容）
│   └── langgraph-workflow.ts  # LangGraph路由
├── types/           # TypeScript类型
└── agents/          # 原有智能体（兼容）
```

### 扩展LangGraph工作流

**添加新节点:**
```typescript
const workflow = new StateGraph({...})
  .addNode('new_node', this.newNodeFunction.bind(this))
  .addEdge('previous_node', 'new_node');
```

**创建新工具:**
```typescript
export const createCustomTool = () => {
  return new DynamicStructuredTool({
    name: 'custom_tool',
    description: '自定义工具描述',
    schema: z.object({
      input: z.string().describe('输入参数')
    }),
    func: async ({ input }) => {
      // 工具逻辑实现
      return JSON.stringify({ result: 'processed' });
    }
  });
};
```

### 自定义路由逻辑
```typescript
// 在 routerNode 中添加自定义条件
if (customBusinessLogic) {
  agentsToExecute = ['custom_agent_flow'];
  executionMode = 'sequential';
  reasoning = '自定义业务逻辑路由';
}
```

## 🆚 版本对比

| 特性 | 简单工作流 | LangGraph工作流 |
|------|-----------|----------------|
| **架构** | 固定流程 | 动态图结构 |
| **路由** | 硬编码 | 智能条件路由 |
| **智能体** | 基础LLM调用 | 工具增强智能体 |
| **并行** | 简单并行 | 优化并行执行 |
| **工具** | 无 | 6类专业工具 |
| **质量** | 基础检查 | 多层质量保证 |
| **监控** | 基础指标 | 完整可观测性 |
| **扩展** | 低 | 高度可扩展 |

## 📚 参考文档

- [LangGraph实施计划](.ht/05-langgraph-architecture-design.md)
- [完整实施总结](.ht/06-implementation-summary.md)
- [LangGraph官方文档](https://langchain-ai.github.io/langgraph/)

## 🤝 贡献

欢迎提交Issue和Pull Request！

### 开发流程
1. Fork项目
2. 创建功能分支
3. 实现新功能或修复
4. 添加测试用例
5. 提交Pull Request

### 代码规范
- 使用TypeScript进行开发
- 遵循ESLint配置
- 保持代码简洁和可读性
- 添加完整的类型定义

---

**版本**: 2.0.0 (LangGraph Edition)
**发布日期**: 2024年12月
**维护团队**: HT Team

🎯 **升级建议**: 推荐从简单工作流迁移到LangGraph工作流以获得更好的性能和功能
